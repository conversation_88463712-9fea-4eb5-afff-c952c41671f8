<script setup lang="ts">
import { ref, computed } from "vue";
import { useForm } from "vee-validate";
const config = useRuntimeConfig();
useHead({
  link: [
    {
      rel: "stylesheet",
      href: `${config.public.payment_0d_domain}/pg_was/css/payment/layer/paymentClient.css`,
    },
  ],
  script: [
    {
      src: `${config.public.payment_0d_domain}/pg_was/js/payment/layer/paymentClient.js`,
      defer: true,
      type: "text/javascript",
    },
  ],
});
const api = useApi();
const route = useRoute() as any;
const toast = useToast();
const router = useRouter();
console.log('route', route.query?.order);
// const currentStep = ref(3); // Initial step

// const isAccept = ref(false);
// const detailProductData = ref() as any;
// const discountRate = ref(0);
// const discount = ref("");
// const introduce = ref("");
// const { values, setValues, defineField } = useForm<ISimOrder>();
// const [payment_type] = defineField("payment_type");

// const { orderResult, orderInfo } = storeToRefs(useSimStore());
// const { merTrxId, invoiceNo, deep_link } = storeToRefs(useMobileStore());
// const { minutes, seconds, isModalOpen } = storeToRefs(useCountdownStore());
// onMounted(async () => {
//   if (route.query.is_mobile) {
//     merTrxId.value = route.query?.merTrxId ?? "";
//     invoiceNo.value = route.query?.invoiceNo ?? "";
//     deep_link.value = route.query?.deep_link ?? "";
//     resInfo.value = {
//       ...route.query,
//       windowType: 1,
//       //@ts-ignore

//       callBackUrl: `${window.location.origin}/insurance/buy/results/${route.query?.order}`,
//     };
//     requestVnPay(resInfo.value);
//     return;
//   }
//   // case ở màn result back về trang cũ
//   if (orderResult && orderResult?.value?.id === route.query?.order) {
//     return router.push("/");
//   }
//   const matchingOrder = orderInfo.value.find(
//     (order: any) => order.order_id == route.query.order
//   );
//   if (matchingOrder) {
//     setValues({ ...matchingOrder });
//   }
// });
// const { data: detailProduct } = await fetchAsyncData(
//   `/v1/products/${+route.params.id}`
// );

// detailProductData.value = detailProduct.value?.data;
// const discountAmount = computed(
//   () =>
//     (detailProductData.value?.price +
//       values.gia_sim +
//       (values.phi_giao_hang ?? 0)) *
//     discountRate.value
// );
// const totalCost = computed(() => {
//   const total =
//     detailProductData.value?.price + values.gia_sim + values.phi_hoa_mang;

//   return total - discountAmount.value;
// });
// const getPayment = async () => {
//   if (isAccept.value === false) {
//     return toast.add({
//       title:
//         "Vui lòng đồng ý với Điều khoản sử dụng & Chính sách riêng tư của Muadi",
//       timeout: 2000,
//       icon: "i-heroicons-x-circle-16-solid",
//       color: "red",

//       ui: {
//         wrapper: "!items-center",
//         position: "top-0 bottom-auto",
//       },
//     });
//   }
//   if (minutes.value === 0 && seconds.value === 0) {
//     isModalOpen.value = true;
//     return;
//   }
//   const { data } = await api.post("/v1/sim-so/create", {
//     body: { ...values },
//   });
//   const res = data?.value?.data;
//   if (data?.value.code === 0) {
//     const orderData = {
//       message: res?.message,
//       id: route?.query?.order,
//       order_id: res?.order_id,
//       order_time: Number(res?.order_time),
//       order_status: res?.status,
//       success: true,
//     };
//     orderResult.value = orderData;
//     orderInfo.value = orderInfo.value.filter(
//       (item: any) => item.order_id !== +route.query?.order
//     );
//     return await getPaymentInfo();
//     // return router.push({
//     //   path: `/payment/result`,
//     // });
//   }
//   return toast.add({
//     title: data.value.message,
//     timeout: 5000,
//     icon: "i-heroicons-x-circle-16-solid",
//     color: "red",
//     ui: {
//       position: "top-0 bottom-auto",
//     },
//   });
// };
// const resInfo = ref<any>();
// const getPaymentInfo = async () => {
//   const { data } = await api.get(
//     `/v1/payments/${+route.query.order}/get-payment-info`
//   );
//   // case đơn không tồn tại
//   if (data.value?.code === CODE.ORDER_NOT_EXIST) {
//     merTrxId.value = "";
//     invoiceNo.value = "";
//     return router.push("/");
//   }
//   merTrxId.value = data.value?.data.merTrxId;
//   invoiceNo.value = data.value?.data.invoiceNo;
//   resInfo.value = {
//     ...data.value?.data,
//     payType: "QR",
//     callBackUrl:
//       route.query.callBackUrl ||
//       `${window.location.origin}/insurance/buy/results/${+route.query?.order}`,
//     windowColor: "#ef5459",
//     reqDomain: `${window.location.origin}`,
//     windowType: isMobile() ? 1 : 0,
//   };
//   requestVnPay(resInfo.value);
// };
// const requestVnPay = (data: any) => {
//   delete data.phone_number;
//   if (data.payType === "QR") {
//     delete data.bankCode;
//   }
//   const form = document.createElement("form");
//   form.method = "POST";
//   form.id = "megapayForm";
//   form.name = "megapayForm";
//   for (const key in data) {
//     if (data.hasOwnProperty(key)) {
//       const input = document.createElement("input");
//       input.type = "hidden";
//       input.name = key;
//       input.value = data[key] === null ? "" : data[key];
//       form.appendChild(input);
//     }
//   }
//   document.body.appendChild(form);
//   window.openPayment(1, config.public.payment_0d_domain);
// };

// const getSimType = computed(() => {
//   if (values.loai_sim === SIM_TYPE.ESIM) return "eSIM";
//   return "SIM vật lý";
// });
// const disabled = computed(() => !isAccept.value || !payment_type.value);
</script>
