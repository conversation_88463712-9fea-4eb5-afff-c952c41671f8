<script setup lang="ts">
import { ref, computed } from "vue";

// Define types for this component
interface InsuranceOrder {
  payment_type?: string;
  gia_sim?: number;
  phi_giao_hang?: number;
  phi_hoa_mang?: number;
  loai_sim?: string;
  order_id?: number;
}

// Constants
const CODE = {
  ORDER_NOT_EXIST: 404
} as const;

const SIM_TYPE = {
  ESIM: 'esim'
} as const;

// Utility functions
const isMobile = () => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth <= 768;
};

// Extend window interface for payment
declare global {
  interface Window {
    openPayment: (type: number, domain: string) => void;
  }
}

const config = useRuntimeConfig();
useHead({
  link: [
    {
      rel: "stylesheet",
      href: `${config.public.payment_0d_domain}/pg_was/css/payment/layer/paymentClient.css`,
    },
  ],
  script: [
    {
      src: `${config.public.payment_0d_domain}/pg_was/js/payment/layer/paymentClient.js`,
      defer: true,
      type: "text/javascript",
    },
  ],
});

const api = useApi();
const route = useRoute() as any;
const toast = useToast();
const router = useRouter();
console.log('route', route.query);

const isAccept = ref(false);
const detailProductData = ref() as any;
const discountRate = ref(0);

// Mock form values for insurance order
const values = ref<InsuranceOrder>({
  payment_type: '',
  gia_sim: 0,
  phi_giao_hang: 0,
  phi_hoa_mang: 0,
  loai_sim: '',
  order_id: 0
});

const setValues = (newValues: Partial<InsuranceOrder>) => {
  values.value = { ...values.value, ...newValues };
};

const payment_type = computed({
  get: () => values.value.payment_type || '',
  set: (value: string) => {
    values.value.payment_type = value;
  }
});

// Mock store states
const orderResult = ref<any>(null);
const orderInfo = ref<any[]>([]);
const merTrxId = ref('');
const invoiceNo = ref('');
const deep_link = ref('');
const minutes = ref(0);
const seconds = ref(0);
const isModalOpen = ref(false);
onMounted(async () => {
  if (route.query.is_mobile) {
    merTrxId.value = route.query?.merTrxId ?? "";
    invoiceNo.value = route.query?.invoiceNo ?? "";
    deep_link.value = route.query?.deep_link ?? "";
    resInfo.value = {
      ...route.query,
      windowType: 1,
      callBackUrl: `${window.location.origin}/insurance/buy/results/${route.query?.order}`,
    };
    requestVnPay(resInfo.value);
    return;
  }
  // case ở màn result back về trang cũ
  if (orderResult && orderResult?.value?.id === route.query?.order) {
    return router.push("/");
  }
  const matchingOrder = orderInfo.value.find(
    (order: any) => order.order_id == route.query.order
  );
  if (matchingOrder) {
    setValues({ ...matchingOrder });
  }

  // Load product details
  try {
    const { data: detailProduct } = await api.get(`/v1/products/${+route.params.id}`);
    detailProductData.value = detailProduct?.data;
  } catch (error) {
    console.error('Failed to load product details:', error);
  }
});

const discountAmount = computed(
  () =>
    (detailProductData.value?.price +
      (values.value.gia_sim || 0) +
      (values.value.phi_giao_hang || 0)) *
    discountRate.value
);

const totalCost = computed(() => {
  const total =
    (detailProductData.value?.price || 0) +
    (values.value.gia_sim || 0) +
    (values.value.phi_hoa_mang || 0);

  return total - discountAmount.value;
});
const getPayment = async () => {
  if (isAccept.value === false) {
    return toast.showError(
      "Vui lòng đồng ý với Điều khoản sử dụng & Chính sách riêng tư của Muadi"
    );
  }
  if (minutes.value === 0 && seconds.value === 0) {
    isModalOpen.value = true;
    return;
  }
  const { data } = await api.post("/v1/orders/buy-insurance", {
    body: { ...values.value },
  });
  const res = data?.value?.data;
  if (data?.value.code === 0) {
    const orderData = {
      message: res?.message,
      id: route?.query?.order,
      order_id: res?.order_id,
      order_time: Number(res?.order_time),
      order_status: res?.status,
      success: true,
    };
    orderResult.value = orderData;
    orderInfo.value = orderInfo.value.filter(
      (item: any) => item.order_id !== +route.query?.order
    );
    return await getPaymentInfo();
  }
  return toast.showError(data.value.message || "Có lỗi xảy ra");
};
const resInfo = ref<any>();

const getPaymentInfo = async () => {
  const { data } = await api.get(
    `/v1/payments/${+route.query.order}/get-payment-info`
  );
  // case đơn không tồn tại
  if (data.value?.code === CODE.ORDER_NOT_EXIST) {
    merTrxId.value = "";
    invoiceNo.value = "";
    return router.push("/");
  }
  merTrxId.value = data.value?.data.merTrxId;
  invoiceNo.value = data.value?.data.invoiceNo;
  resInfo.value = {
    ...data.value?.data,
    payType: "QR",
    callBackUrl:
      route.query.callBackUrl ||
      `${window.location.origin}/insurance/buy/results/${+route.query?.order}`,
    windowColor: "#ef5459",
    reqDomain: `${window.location.origin}`,
    windowType: isMobile() ? 1 : 0,
  };
  requestVnPay(resInfo.value);
};

const requestVnPay = (data: any) => {
  delete data.phone_number;
  if (data.payType === "QR") {
    delete data.bankCode;
  }
  const form = document.createElement("form");
  form.method = "POST";
  form.id = "megapayForm";
  form.name = "megapayForm";
  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      const input = document.createElement("input");
      input.type = "hidden";
      input.name = key;
      input.value = data[key] === null ? "" : data[key];
      form.appendChild(input);
    }
  }
  document.body.appendChild(form);

  // Check if openPayment exists before calling
  if (typeof window !== 'undefined' && window.openPayment) {
    window.openPayment(1, config.public.payment_0d_domain);
  } else {
    console.warn('openPayment function not available');
  }
};

// Export computed values that might be used in template
const getSimType = computed(() => {
  if (values.value.loai_sim === SIM_TYPE.ESIM) return "eSIM";
  return "SIM vật lý";
});

const disabled = computed(() => !isAccept.value || !payment_type.value);
</script>
